package cn.genn.ai.hub.app.application.dto.rtc;

import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallConfirmEnum;
import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallTypeEnum;
import cn.genn.core.model.enums.DeletedEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RTCPitfallItemDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "隐患信息id")
    private Long pitfallId;

    @Schema(description = "隐患编号")
    private String pitfallNo;

    @Schema(description = "类型,1:GENERAL-一般隐患,2:MAJOR-重大隐患")
    private RTCPitfallTypeEnum pitfallType;

    @Schema(description = "确认状态,0:UN_CONFIRM-待确认,1:NO_NEED_RESOLVE-无需解决,2:NEED_RESOLVE-需要解决")
    private RTCPitfallConfirmEnum confirmStatus;

    @Schema(description = "隐患内容")
    private RTCPitfallContentDTO pitfallContent;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedEnum deleted;
}
