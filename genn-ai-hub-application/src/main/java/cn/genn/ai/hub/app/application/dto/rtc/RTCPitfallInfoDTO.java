package cn.genn.ai.hub.app.application.dto.rtc;

import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallStatusEnum;
import cn.genn.core.model.enums.DeletedEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RTCPitfallInfoDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "智能体id")
    private String appId;

    @Schema(description = "隐患编号")
    private String pitfallNo;

    @Schema(description = "状态,1:UN_SUBMIT-待上报,2:TBC-待确认,3:UN_RESOLVE-待解决,4:RESOLVED-已解决,5:无需解决")
    private RTCPitfallStatusEnum status;

    @Schema(description = "隐患区域")
    private String pitfallArea;

    @Schema(description = "隐患上传内容")
    private List<String> uploadContent;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "上报时间")
    private LocalDateTime submitTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "解决时间")
    private LocalDateTime resolveTime;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedEnum deleted;
}
