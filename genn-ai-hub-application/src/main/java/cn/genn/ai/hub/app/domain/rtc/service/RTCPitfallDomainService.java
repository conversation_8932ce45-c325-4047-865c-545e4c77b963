package cn.genn.ai.hub.app.domain.rtc.service;

import cn.genn.ai.hub.app.domain.rtc.model.aggregates.RTCPitfallAgg;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallAnalysisInfo;
import cn.genn.ai.hub.app.domain.rtc.repository.IRTCPitfallInfoRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Service
@Slf4j
public class RTCPitfallDomainService {

    @Resource
    private IRTCPitfallInfoRepository irtcPitfallInfoRepository;

    public RTCPitfallAgg findRTCPitfallAgg(Long id) {
        return irtcPitfallInfoRepository.selectRTCPitfallAgg(id);
    }

    public void add(RTCPitfallAgg rtcPitfallAgg){
        rtcPitfallAgg.initPitfallNo();
        irtcPitfallInfoRepository.add(rtcPitfallAgg);
    }
    public void save(RTCPitfallAgg rtcPitfallAgg){
        irtcPitfallInfoRepository.save(rtcPitfallAgg);
    }

    public void update(RTCPitfallAgg rtcPitfallAgg) {
        irtcPitfallInfoRepository.update(rtcPitfallAgg);
    }

    public void addPitfallAnalysis(RTCPitfallAnalysisInfo rtcPitfallAnalysisInfo) {
        irtcPitfallInfoRepository.addPitfallAnalysis(rtcPitfallAnalysisInfo);
    }
}
