package cn.genn.ai.hub.app.application.dto.rtc;

import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallAnalysisTypeEnum;
import cn.genn.core.model.enums.DeletedEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RTCPitfallAnalysisDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "智能体id")
    private String appId;

    @Schema(description = "类型")
    private RTCPitfallAnalysisTypeEnum type;

    @Schema(description = "AI分析内容")
    private String content;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedEnum deleted;
}
