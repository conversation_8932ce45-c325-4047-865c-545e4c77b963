package cn.genn.ai.hub.app.application.enums.rtc;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-21
 */
@Getter
public enum RTCPitfallAnalysisTypeEnum {
    SEVEN_DAY(1, "隐患七日分析"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    RTCPitfallAnalysisTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, RTCPitfallAnalysisTypeEnum> VALUES = new HashMap<>();
    static {
        for (final RTCPitfallAnalysisTypeEnum item : RTCPitfallAnalysisTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RTCPitfallAnalysisTypeEnum of(int code) {
        return VALUES.get(code);
    }

}
