package cn.genn.ai.hub.app.application.dto.rtc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RTCPitfallContentDTO {

    @Schema(description = "隐患描述")
    private String description;

    @Schema(description = "检查依据")
    private String checkBasis;

    @Schema(description = "整改措施")
    private String measure;

    @Schema(description = "解决方案")
    private String solution;

    @Schema(description = "附件")
    private List<String> files;
}
