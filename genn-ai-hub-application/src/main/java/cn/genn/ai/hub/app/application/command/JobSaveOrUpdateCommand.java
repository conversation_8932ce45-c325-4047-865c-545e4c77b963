package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.dto.govow.ContentSupplement;
import cn.genn.ai.hub.app.application.enums.ChatMode;
import cn.genn.job.xxl.model.task.TaskStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.NotBlank;

import java.util.List;

@Data
@Accessors(chain = true)
public class JobSaveOrUpdateCommand {

    @Schema(description = "任务id")
    private Long id;

    @Schema(description = "任务名称")
    @NotBlank(message = "任务名称不能为空")
    private String name;

    @Schema(description = "任务指令")
    @NotBlank(message = "任务指令不能为空")
    private String content;

    @Schema(description = "输出模式", example = "common")
    @NotNull(message = "输出模式不能为空")
    private ChatMode chatMode;

    @Schema(description = "任务状态,running自动启用,stop暂停")
    private TaskStatusEnum status;

    @Schema(description = "cron表达式")
    @NotBlank(message = "任务时间不能为空")
    private String cron;

    @Schema(description = "问题补充")
    private List<ContentSupplement> supplements;

}
