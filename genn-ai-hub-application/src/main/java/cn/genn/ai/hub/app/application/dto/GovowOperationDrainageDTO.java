package cn.genn.ai.hub.app.application.dto;

import cn.genn.core.model.enums.DeletedTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 格物运营-用户引流DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "格物运营-用户引流DTO")
public class GovowOperationDrainageDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "缓存键")
    private String cacheKey;

    @Schema(description = "问题简介")
    private String questionShort;

    @Schema(description = "问题详情")
    private String questionDetail;

    @Schema(description = "部门")
    private String department;

    @Schema(description = "其他信息")
    private String extraInfo;

    @Schema(description = "飞书用户ID")
    private String openId;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "头像")
    private String createUserAvatar;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedTypeEnum deleted;

} 