package cn.genn.ai.hub.app.application.dto.rtc;

import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RTCPitfallDetailDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "智能体id")
    private String appId;

    @Schema(description = "智能体名称")
    private String appName;

    @Schema(description = "隐患编号")
    private String pitfallNo;

    @Schema(description = "状态,1:UN_SUBMIT-待上报,2:TBC-待确认,3:UN_RESOLVE-待解决,4:RESOLVED-已解决,5:无需解决")
    private RTCPitfallStatusEnum status;

    @Schema(description = "隐患区域")
    private String pitfallArea;

    @Schema(description = "隐患上传内容")
    private List<String> uploadContent;

    @Schema(description = "隐患项")
    private List<RTCPitfallItemDTO> pitfallItems;
}
