package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.RTCPitfallAssembler;
import cn.genn.ai.hub.app.application.command.rtc.RTCPitfallConfirmCommand;
import cn.genn.ai.hub.app.application.command.rtc.RTCPitfallItemSubmitCommand;
import cn.genn.ai.hub.app.application.command.rtc.RTCPitfallSubmitCommand;
import cn.genn.ai.hub.app.application.dto.AgentCompleteRespDTO;
import cn.genn.ai.hub.app.application.dto.request.AgentRequest;
import cn.genn.ai.hub.app.application.dto.rtc.*;
import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallStatusEnum;
import cn.genn.ai.hub.app.application.query.rtc.RTCPitfallPageQuery;
import cn.genn.ai.hub.app.application.service.query.RTCPitfallQueryService;
import cn.genn.ai.hub.app.domain.rtc.model.aggregates.RTCPitfallAgg;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallAnalysisInfo;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallInfo;
import cn.genn.ai.hub.app.domain.rtc.service.RTCPitfallDomainService;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.config.RTCProperties;
import cn.genn.ai.hub.app.infrastructure.constant.AgentAppConstants;
import cn.genn.ai.hub.app.infrastructure.exception.AgentInvokeErrorCode;
import cn.genn.ai.hub.app.infrastructure.exception.AgentInvokeException;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.external.agent.AgentInvokeUtils;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-13
 */
@Service
@Slf4j
public class RTCPitfallActionService {

    @Resource
    private RTCPitfallDomainService rtcPitfallDomainService;

    @Resource
    private RTCPitfallAssembler rtcPitfallAssembler;

    private WebClient webClient;

    @Resource
    private AgentInvokeUtils agentInvokeUtils;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private GennAIHubProperties gennAIHubProperties;

    @Resource
    private RTCPitfallQueryService rtcPitfallQueryService;

    @PostConstruct
    public void initClient() {
        WebClient.Builder webClientBuilder = WebClient.builder().codecs(configurer -> configurer
            .defaultCodecs()
            .maxInMemorySize(16 * 1024 * 1024));
        webClient = webClientBuilder.build();
    }

    @Transactional(rollbackFor = Exception.class)
    public RTCPitfallInfoDTO submit(RTCPitfallSubmitCommand command) {
        RTCPitfallAgg rtcPitfallAgg;
        if (Objects.nonNull(command.getId())){
            rtcPitfallAgg = rtcPitfallDomainService.findRTCPitfallAgg(command.getId());
            if (Objects.isNull(rtcPitfallAgg)){
                throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
            }
            rtcPitfallAssembler.fillRTCPitfallAgg(rtcPitfallAgg, command);
            rtcPitfallAgg.getRtcPitfallInfo().setSubmitTime(LocalDateTime.now());
            rtcPitfallDomainService.save(rtcPitfallAgg);
        }else {
            rtcPitfallAgg = rtcPitfallAssembler.toRTCPitfallAgg(command);
            rtcPitfallAgg.getRtcPitfallInfo().setSubmitTime(LocalDateTime.now());
            rtcPitfallDomainService.add(rtcPitfallAgg);
        }
        return rtcPitfallAssembler.toRTCPitfallInfoDTO(rtcPitfallDomainService.findRTCPitfallAgg(rtcPitfallAgg.getRtcPitfallInfo().getId()).getRtcPitfallInfo());
    }

    @Transactional(rollbackFor = Exception.class)
    public RTCPitfallInfoDTO confirm(RTCPitfallConfirmCommand command) {
        RTCPitfallAgg rtcPitfallAgg = rtcPitfallDomainService.findRTCPitfallAgg(command.getId());
        RTCPitfallInfo rtcPitfallInfo = rtcPitfallAgg.getRtcPitfallInfo();
        rtcPitfallInfo.setStatus(command.getStatus());

        List<RTCPitfallItemSubmitCommand> pitfallItems = command.getPitfallItems();
        if (!Objects.equals(RTCPitfallStatusEnum.TBC, command.getStatus())){
            rtcPitfallInfo.setConfirmTime(LocalDateTime.now());
        }
        rtcPitfallAssembler.fillConfirmRTCPitfallItem(rtcPitfallAgg.getRtcPitfallItems(), pitfallItems);
        rtcPitfallDomainService.update(rtcPitfallAgg);
        return rtcPitfallAssembler.toRTCPitfallInfoDTO(rtcPitfallDomainService.findRTCPitfallAgg(command.getId()).getRtcPitfallInfo());
    }

    @Transactional(rollbackFor = Exception.class)
    public RTCPitfallInfoDTO resolve(RTCPitfallConfirmCommand command) {
        RTCPitfallAgg rtcPitfallAgg = rtcPitfallDomainService.findRTCPitfallAgg(command.getId());
        RTCPitfallInfo rtcPitfallInfo = rtcPitfallAgg.getRtcPitfallInfo();
        rtcPitfallInfo.setStatus(command.getStatus());
        if (!Objects.equals(RTCPitfallStatusEnum.UN_RESOLVE, command.getStatus())){
            rtcPitfallInfo.setResolveTime(LocalDateTime.now());
        }
        rtcPitfallAssembler.fillResolveRTCPitfallItem(rtcPitfallAgg.getRtcPitfallItems(), command.getPitfallItems());
        rtcPitfallDomainService.update(rtcPitfallAgg);
        return rtcPitfallAssembler.toRTCPitfallInfoDTO(rtcPitfallDomainService.findRTCPitfallAgg(command.getId()).getRtcPitfallInfo());
    }

    @Transactional(rollbackFor = Exception.class)
    public void createPitfallAnalysis(RTCPitfallAnalysisInfo rtcPitfallAnalysisInfo){
        rtcPitfallDomainService.addPitfallAnalysis(rtcPitfallAnalysisInfo);
    }

    public RTCPitfallAnalysisDTO pitfallAnalysis() {
        Optional<RTCProperties.Agent> first = gennAIHubProperties.getRtc().getAgents().get(1L)
            .stream().filter(agent -> agent.getType().equals(RTCAgentType.ANALYSIS.getCode())).findFirst();
        if (first.isEmpty()) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        RTCPitfallPageQuery query = new RTCPitfallPageQuery();
        query.setCreateUserId(CurrentUserHolder.getUserId());
        LocalDate nowDate = LocalDate.now();
        query.setCreateTimeIndex(nowDate.minusDays(6).atStartOfDay());
        query.setCreateTimeEnd(LocalDateTime.now());
        List<RTCPitfallPageDTO> sevenPitfall = rtcPitfallQueryService.getSubmitPitfall(query);
        List<RTCPitfallDTO> rtcPitfallDTO = rtcPitfallAssembler.toRTCPitfallDTO(sevenPitfall);
        RTCPitfallActionService proxy = SpringUtil.getBean(RTCPitfallActionService.class);
        AgentCompleteRespDTO agentCompleteRespDTO = proxy.invokeStreamGetAnswer(first.get().getAppId(), JsonUtils.toJson(rtcPitfallDTO));
        if (agentCompleteRespDTO != null) {
            String answer = agentCompleteRespDTO.getAnswer();
            RTCPitfallAnalysisInfo rtcPitfallAnalysisInfo = new RTCPitfallAnalysisInfo();
            rtcPitfallAnalysisInfo.setTenantId(CurrentUserHolder.getTenantId());
            rtcPitfallAnalysisInfo.setAppId(first.get().getAppId());
            rtcPitfallAnalysisInfo.setContent(answer);
            proxy.createPitfallAnalysis(rtcPitfallAnalysisInfo);
            return RTCPitfallAnalysisDTO.builder().content(answer).build();
        }
        return new RTCPitfallAnalysisDTO();
    }

    public AgentCompleteRespDTO invokeStreamGetAnswer(String appId, String userMessage)
        throws AgentInvokeException {
        AgentRequest request = buildAgentRequest(appId, userMessage);
        try {
            log.info("invoke stream get answer request body: {}", JsonUtils.toJson(request));
            AgentCompleteRespDTO completeResponse = getCompleteResponse(
                gennAIHubProperties.getAgent().getInvokeDomain() + AgentAppConstants.INVOKE_APP_URL,
                JsonUtils.toJson(request),
                gennAIHubProperties.getAgent().getAuthorization()
            );
            log.info("invoke stream get answer response: {}", completeResponse);
            return completeResponse;
        } catch (Exception e) {
            log.error("Error getting agent answer for appId {}: {}", appId, e.getMessage());
            return null;
        }
    }

    private AgentCompleteRespDTO getCompleteResponse(String apiUrl, String body, String authorization) throws AgentInvokeException {
        try {
            CompletableFuture<AgentCompleteRespDTO> resultFuture = new CompletableFuture<>();
            StringBuilder answerBuilder = new StringBuilder();
            AtomicReference<JsonNode> lastInteractiveDataNode = new AtomicReference<>(null);
            webClient.post()
                .uri(apiUrl)
                .headers(h -> h.addAll(agentInvokeUtils.fetchJsonHeader(authorization)))
                .bodyValue(body)
                .retrieve()
                .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<String>>() {
                })
                .doOnNext(sse -> {
                    log.info("SSE Received: event='{}', data='{}'", sse.event(), sse.data());
                    String eventType = sse.event();
                    String eventData = sse.data();

                    if ("[DONE]".equals(eventData)) {
                        log.info("SSE stream [DONE] marker received.");
                        return; // 在 doOnComplete 中处理
                    }

                    try {
                        if (eventType != null) {
                            switch (eventType) {
                                case "answer", "fastAnswer" -> processAnswer(eventType, eventData, answerBuilder);
                                case "interactive" -> {
                                    lastInteractiveDataNode.set(objectMapper.readTree(eventData));
                                    log.info("Stored interactive event data.");
                                }
                                case "flowResponses" -> log.debug("Received flowResponses event, data: {}", eventData);
                                // 当前 DTO 不需要，忽略
                                default ->
                                    log.warn("Unsupported or unhandled SSE event type: {} or data format. Data: {}", eventType, eventData);
                            }
                        }
                    } catch (JsonProcessingException e) {
                        log.error("Failed to parse SSE data as JsonNode. Event: {}, Data: {}, Error: {}", eventType, eventData, e.getMessage(), e);
                    }
                })
                .doOnComplete(() -> {
                    log.info("SSE stream completed.");
                    AgentCompleteRespDTO finalDto = new AgentCompleteRespDTO();
                    finalDto.setAnswer(answerBuilder.toString());
                    JsonNode interactiveData = lastInteractiveDataNode.get();
                    fillInteractive(interactiveData, finalDto);
                    resultFuture.complete(finalDto);
                })
                .doOnError(throwable -> {
                    log.error("获取流式响应失败, url: {}, body: {}", apiUrl, body, throwable);
                    resultFuture.completeExceptionally(throwable);
                })
                .subscribe(); // 记得 subscribe 启动流式请求

            return resultFuture.get();
        } catch (Exception e) {
            log.error("获取流式响应失败, url: {}, body: {}", apiUrl, body, e);
            throw new AgentInvokeException(AgentInvokeErrorCode.COMPLETE, e.getMessage());
        }
    }

    private void processAnswer(String eventType, String data, StringBuilder builder) {
        JsonNode answer;
        try {
            answer = objectMapper.readTree(data);
        } catch (JsonProcessingException e) {
            log.error("解析 JSON 失败, data: {}", data, e);
            return;
        }
        JsonNode choices = answer.path("choices");

        for (JsonNode choice : choices) {
            JsonNode delta = choice.path("delta");
            String content = delta.path("content").asText(null);

            if (content != null) {
                if ("fastAnswer".equals(eventType) && content.startsWith("\n")) {
                    // 去掉开头的 "\n" (两个字符) （特殊处理）
                    content = content.substring(1);
                }
                builder.append(content);
            }
        }
    }

    private void fillInteractive(JsonNode interactiveData, AgentCompleteRespDTO finalDto) {
        if (interactiveData != null && interactiveData.has("interactive")) {
            JsonNode interactiveNode = interactiveData.path("interactive");
            AgentCompleteRespDTO.InteractiveContent dtoInteractive = new AgentCompleteRespDTO.InteractiveContent();
            dtoInteractive.setType(interactiveNode.path("type").asText(null));

            if (interactiveNode.has("params")) {
                JsonNode paramsNode = interactiveNode.path("params");
                AgentCompleteRespDTO.Params dtoParams = new AgentCompleteRespDTO.Params();
                dtoParams.setDescription(paramsNode.path("description").asText(null));

                // Populate inputForm if present
                if (paramsNode.has("inputForm") && paramsNode.path("inputForm").isArray()) {
                    List<AgentCompleteRespDTO.InputFormItem> inputFormItems = new ArrayList<>();
                    for (JsonNode itemNode : paramsNode.path("inputForm")) {
                        AgentCompleteRespDTO.InputFormItem item = new AgentCompleteRespDTO.InputFormItem();
                        item.setType(itemNode.path("type").asText(null));
                        item.setKey(itemNode.path("key").asText(null));
                        item.setLabel(itemNode.path("label").asText(null));
                        item.setDescription(itemNode.path("description").asText(null));
                        item.setValue(itemNode.path("value").asText(null));
                        item.setDefaultValue(itemNode.path("defaultValue").asText(null));
                        item.setValueType(itemNode.path("valueType").asText(null));
                        item.setRequired(itemNode.path("required").asBoolean(false));
                        Optional.ofNullable(itemNode.get("maxLength"))
                            .ifPresent(maxLengthNode -> item.setMaxLength(maxLengthNode.asInt()));
                        // 正确处理 list 字段为对象类型
                        if (itemNode.has("list") && itemNode.path("list").isArray()) {
                            List<AgentCompleteRespDTO.InputSelectOption> listValues = new ArrayList<>();
                            for (JsonNode listItem : itemNode.path("list")) {
                                AgentCompleteRespDTO.InputSelectOption option = new AgentCompleteRespDTO.InputSelectOption();
                                option.setLabel(listItem.path("label").asText(null));
                                option.setValue(listItem.path("value").asText(null));
                                listValues.add(option);
                            }
                            item.setList(listValues);
                        } else {
                            item.setList(null);
                        }
                        inputFormItems.add(item);
                    }
                    dtoParams.setInputForm(inputFormItems);
                }

                // Populate userSelectOptions if present
                if (paramsNode.has("userSelectOptions") && paramsNode.path("userSelectOptions").isArray()) {
                    List<AgentCompleteRespDTO.UserSelectOption> selectOptions = new ArrayList<>();
                    for (JsonNode optionNode : paramsNode.path("userSelectOptions")) {
                        AgentCompleteRespDTO.UserSelectOption option = new AgentCompleteRespDTO.UserSelectOption();
                        option.setKey(optionNode.path("key").asText(null));
                        option.setValue(optionNode.path("value").asText(null));
                        selectOptions.add(option);
                    }
                    dtoParams.setUserSelectOptions(selectOptions);
                }
                dtoInteractive.setParams(dtoParams);
            }
            finalDto.setInteractive(dtoInteractive);
        }
    }

    private AgentRequest buildAgentRequest(String appId,  String userMessage) {
        AgentRequest.MessageDTO messageDTO = AgentRequest.MessageDTO.builder()
            .content(userMessage)
            .role("user")
            .hideInUI(false)
            .build();

        return AgentRequest.builder()
            .appId(appId)
            .detail(true)
            .stream(true)
            .messages(List.of(messageDTO))
            .build();
    }
}
