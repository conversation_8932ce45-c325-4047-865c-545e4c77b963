package cn.genn.ai.hub.app.application.enums.rtc;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Getter
public enum RTCPitfallStatusEnum {
    UN_SUBMIT(1, "待上报"),
    TBC(2, "待确认"),
    UN_RESOLVE(3, "待解决"),
    RESOLVED(4, "已解决"),
    NO_NEED_RESOLVE(5, "无需解决"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    RTCPitfallStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, RTCPitfallStatusEnum> VALUES = new HashMap<>();
    static {
        for (final RTCPitfallStatusEnum item : RTCPitfallStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RTCPitfallStatusEnum of(int code) {
        return VALUES.get(code);
    }

}
