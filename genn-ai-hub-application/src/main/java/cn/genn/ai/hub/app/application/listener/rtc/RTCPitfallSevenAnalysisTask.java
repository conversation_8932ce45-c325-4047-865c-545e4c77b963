package cn.genn.ai.hub.app.application.listener.rtc;

import cn.genn.ai.hub.app.application.assembler.RTCPitfallAssembler;
import cn.genn.ai.hub.app.application.dto.AgentCompleteRespDTO;
import cn.genn.ai.hub.app.application.dto.rtc.RTCAgentType;
import cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallDTO;
import cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallPageDTO;
import cn.genn.ai.hub.app.application.query.rtc.RTCPitfallPageQuery;
import cn.genn.ai.hub.app.application.service.action.RTCPitfallActionService;
import cn.genn.ai.hub.app.application.service.query.RTCPitfallQueryService;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallAnalysisInfo;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.config.RTCProperties;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.component.AbstractJobHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-20
 */
@Slf4j
@Component
public class RTCPitfallSevenAnalysisTask extends AbstractJobHandler {

    @Resource
    private RTCPitfallQueryService rtcPitfallQueryService;

    @Resource
    private RTCPitfallAssembler rtcPitfallAssembler;

    @Resource
    private RTCPitfallActionService rtcPitfallActionService;

    @Resource
    private GennAIHubProperties gennAIHubProperties;

    @Override
    public void doExecute() {
        log.info("RTCPitfallSevenAnalysisTask begin");
        Optional<RTCProperties.Agent> first = gennAIHubProperties.getRtc().getAgents().get(1L)
            .stream().filter(agent -> agent.getType().equals(RTCAgentType.ANALYSIS.getCode())).findFirst();
        if (first.isEmpty()) {
            log.error("RTCPitfallSevenAnalysisTask.doExecute error agent is null");
            return;
        }
        RTCPitfallPageQuery query = new RTCPitfallPageQuery();
        LocalDate nowDate = LocalDate.now();
        query.setCreateTimeIndex(nowDate.minusDays(7).atStartOfDay());
        query.setCreateTimeEnd(nowDate.atStartOfDay());
        List<RTCPitfallPageDTO> sevenPitfall = rtcPitfallQueryService.getSubmitPitfall(query);

        Map<Long, List<RTCPitfallPageDTO>> map = sevenPitfall.stream().collect(Collectors.groupingBy(RTCPitfallPageDTO::getCreateUserId));
        map.forEach((createUserId, pitfallList) -> {
            try {
                List<RTCPitfallDTO> rtcPitfallDTO = rtcPitfallAssembler.toRTCPitfallDTO(sevenPitfall);
                Long tenantId = pitfallList.stream().map(RTCPitfallPageDTO::getTenantId).findFirst().orElse(0L);
                AgentCompleteRespDTO agentCompleteRespDTO = rtcPitfallActionService.invokeStreamGetAnswer(first.get().getAppId(), JsonUtils.toJson(rtcPitfallDTO));
                if (agentCompleteRespDTO != null) {
                    String answer = agentCompleteRespDTO.getAnswer();
                    RTCPitfallAnalysisInfo rtcPitfallAnalysisInfo = new RTCPitfallAnalysisInfo();
                    rtcPitfallAnalysisInfo.setTenantId(tenantId);
                    rtcPitfallAnalysisInfo.setCreateUserId(createUserId);
                    rtcPitfallAnalysisInfo.setUpdateUserId(createUserId);
                    rtcPitfallAnalysisInfo.setAppId(first.get().getAppId());
                    rtcPitfallAnalysisInfo.setContent(answer);
                    rtcPitfallActionService.createPitfallAnalysis(rtcPitfallAnalysisInfo);
                }
            } catch (Exception e) {
                log.error("RTCPitfallSevenAnalysisTask.doExecute error createUserId:{}", createUserId, e);
            }
        });
        log.info("RTCPitfallSevenAnalysisTask end");

    }
}
