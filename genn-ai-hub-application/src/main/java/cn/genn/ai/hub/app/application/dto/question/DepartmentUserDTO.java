package cn.genn.ai.hub.app.application.dto.question;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 部门人员信息DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户飞书openid")
    private String openId;

    @Schema(description = "问题出现次数")
    private Integer questionCount;

    @Schema(description = "最后提问时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastQuestionTime;

    @Schema(description = "最下级部门")
    private String lastDepartment;

    @Schema(description = "一级部门")
    private String firstDepartment;

    @Schema(description = "用户信息")
    private UserInfoDTO userInfo;
}

