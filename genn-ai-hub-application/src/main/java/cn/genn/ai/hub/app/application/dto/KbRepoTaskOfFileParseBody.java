package cn.genn.ai.hub.app.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 解析文件任务请求体
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoTaskOfFileParseBody implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long tenantId;

    private Long repoId;

    private Long collectionId;

    private Long fileId;

    private String externalFileId;

    private String externalFileUrl;

    private String fileName;

    private String contentType;

    private boolean deepParse;

    private Boolean smartChunkSummary;
}

