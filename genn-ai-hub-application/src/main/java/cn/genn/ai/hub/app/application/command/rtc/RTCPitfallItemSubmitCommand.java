package cn.genn.ai.hub.app.application.command.rtc;

import cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallContentDTO;
import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallConfirmEnum;
import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RTCPitfallItemSubmitCommand {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "隐患信息id")
    private Long pitfallId;

    @Schema(description = "隐患编号")
    private String pitfallNo;

    @Schema(description = "类型,1:GENERAL-一般隐患,2:MAJOR-重大隐患")
    private RTCPitfallTypeEnum pitfallType;

    @Schema(description = "确认状态,0:UN_CONFIRM-待确认,1:NO_NEED_RESOLVE-无需解决,2:NEED_RESOLVE-需要解决")
    private RTCPitfallConfirmEnum confirmStatus;

    @Schema(description = "隐患内容")
    private RTCPitfallContentDTO pitfallContent;
}
