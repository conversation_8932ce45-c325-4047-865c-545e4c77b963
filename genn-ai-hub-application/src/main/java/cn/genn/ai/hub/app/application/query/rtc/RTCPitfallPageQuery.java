package cn.genn.ai.hub.app.application.query.rtc;

import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallStatusEnum;
import cn.genn.core.model.page.PageSortQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AgentInfo查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RTCPitfallPageQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "状态,1:UN_SUBMIT-待上报,2:TBC-待确认,3:UN_RESOLVE-待解决,4:RESOLVED-已解决,5:无需解决")
    private RTCPitfallStatusEnum status;

    @Schema(description = "创建时间-开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeIndex;

    @Schema(description = "创建时间-结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeEnd;

    @Schema(description = "创建人")
    private Long createUserId;
}

