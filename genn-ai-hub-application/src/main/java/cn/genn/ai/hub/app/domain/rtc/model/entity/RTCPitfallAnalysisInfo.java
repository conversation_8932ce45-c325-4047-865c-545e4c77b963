package cn.genn.ai.hub.app.domain.rtc.model.entity;

import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallAnalysisTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-18
 */
@Data
public class RTCPitfallAnalysisInfo {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 智能体id
     */
    private String appId;

    /**
     * 类型
     * 1: SEVEN_DAT-隐患7日分析
     */
    private RTCPitfallAnalysisTypeEnum type;

    /**
     * AI分析内容
     */
    private String content;

    /**
     * 创建人
     */
    private Long createUserId;
}
