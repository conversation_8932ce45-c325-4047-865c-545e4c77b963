package cn.genn.ai.hub.app.infrastructure.repository.mapper;

import cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallPageDTO;
import cn.genn.ai.hub.app.application.query.rtc.RTCPitfallPageQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.RTCPitfallInfoPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
public interface RTCPitfallInfoMapper extends BaseMapper<RTCPitfallInfoPO> {
    Page<RTCPitfallPageDTO> selectPitfallPage(Page<RTCPitfallPageDTO> page, @Param("query") RTCPitfallPageQuery query);
}
