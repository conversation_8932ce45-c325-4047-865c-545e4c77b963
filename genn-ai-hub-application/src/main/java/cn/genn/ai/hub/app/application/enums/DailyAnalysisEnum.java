package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DailyAnalysisEnum {

    QUESTION("question", "集团日报分析"),
    RECOMMEND("recommend", "集团订阅提醒"),

    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

}
