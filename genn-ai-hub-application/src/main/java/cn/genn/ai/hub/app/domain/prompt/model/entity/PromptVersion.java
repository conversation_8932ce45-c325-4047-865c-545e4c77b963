package cn.genn.ai.hub.app.domain.prompt.model.entity;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.enums.DeletedEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 提示词版本
 * @date 2025-05-14
 */
@Data
public class PromptVersion {

    private Long id;

    private Long tenantId;

    private Long promptId;

    private String version;

    private String content;

    private String description;

    private LocalDateTime createTime;

    private Long createUserId;

    private String createUserName;

    private LocalDateTime updateTime;

    private Long updateUserId;

    private String updateUserName;

    private DeletedEnum deleted;

    public static PromptVersion create(String version, String content, String description) {
        PromptVersion promptVersion = new PromptVersion();
        promptVersion.setVersion(version);
        promptVersion.setContent(content);
        promptVersion.setDescription(description);
        return promptVersion;
    }

    /**
     * 更新版本内容和描述（主要用于草稿版本）。
     * 对于已发布的版本，通常不建议直接修改，而是创建新版本。
     */
    public void updateContent(String newContent, String newDescription) {
        if (newContent == null) {
            throw new BusinessException("更新时，版本内容不能为空。");
        }
        this.content = newContent;
        this.description = newDescription != null ? newDescription : this.description; // 如果新描述为null，则保留旧描述
    }
}
