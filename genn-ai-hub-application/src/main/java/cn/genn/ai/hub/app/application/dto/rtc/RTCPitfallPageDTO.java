package cn.genn.ai.hub.app.application.dto.rtc;

import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallStatusEnum;
import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RTCPitfallPageDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "状态,1:UN_SUBMIT-待上报,2:TBC-待确认,3:UN_RESOLVE-待解决,4:RESOLVED-已解决,5:无需解决")
    private RTCPitfallStatusEnum status;

    @Schema(description = "类型,1:GENERAL-一般隐患,2:MAJOR-重大隐患")
    private RTCPitfallTypeEnum pitfallType;

    @Schema(description = "隐患描述")
    private String description;

    @Schema(description = "隐患区域")
    private String pitfallArea;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "上报时间")
    private LocalDateTime submitTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "解决时间")
    private LocalDateTime resolveTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "隐患内容")
    private RTCPitfallContentDTO pitfallContent;
}
