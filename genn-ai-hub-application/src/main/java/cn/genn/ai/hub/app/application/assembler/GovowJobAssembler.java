package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.JobSaveOrUpdateCommand;
import cn.genn.ai.hub.app.application.command.govow.JobCompletionsCommand;
import cn.genn.ai.hub.app.application.dto.govow.ContentSupplement;
import cn.genn.ai.hub.app.application.dto.govow.JobTasksDTO;
import cn.genn.ai.hub.app.application.enums.ChatMode;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.model.XxlJobScheduleTypeEnum;
import cn.genn.job.xxl.model.task.*;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import jodd.bean.BeanUtil;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GovowJobAssembler {

    default TaskSaveOrUpdateCommand jobCommand2TaskCommand(JobSaveOrUpdateCommand command,String bizKey){
        TaskSaveOrUpdateCommand taskCommand = new TaskSaveOrUpdateCommand();
        if(ObjUtil.isNotNull(command.getId())){
            taskCommand.setId(command.getId());
        }
        JobTaskPlan plan = new JobTaskPlan();
        plan.setType(XxlJobScheduleTypeEnum.CRON);
        plan.setCron(command.getCron());
        JobTaskContext context = new JobTaskContext();
        context.setTaskType(JobTaskTypeEnum.LOCAL);
        context.setReqTimeout(3600L);
        context.setClassName("cn.genn.ai.hub.app.application.service.govow.GovowJobService");
        context.setMethodName("completions");
        Map<String, Object> params = new HashMap<>();
        params.put("userId", CurrentUserHolder.getUserId());
        params.put("chatMode", command.getChatMode().getCode());
        params.put("content", command.getContent());
        if(CollUtil.isNotEmpty(command.getSupplements())){
            params.put("supplements", JsonUtils.toJson(command.getSupplements()));
        }
        context.setParams(params);
        taskCommand.setName(command.getName());
        taskCommand.setBizKey(bizKey);
        taskCommand.setPlan(plan);
        taskCommand.setContent(context);
        taskCommand.setPriority(0);
        taskCommand.setStatus(command.getStatus());
        return taskCommand;
    }

    List<JobTasksDTO> task2Job(List<JobScheduledTasksDTO> dtos);

    default JobTasksDTO task2Job(JobScheduledTasksDTO dto) {
        JobTasksDTO result = new JobTasksDTO();
        BeanUtils.copyProperties(dto, result);
        result.setCron(dto.getPlan().getCron());
        Map<String, Object> params = dto.getContent().getParams();
        String chatMode = (String)params.get("chatMode");
        String content = (String)params.get("content");
        List<ContentSupplement> supplement = new ArrayList<>();
        Object supplement1 = params.get("supplements");
        if(ObjUtil.isNotNull(supplement1)){
            supplement = JsonUtils.parseToList((String) supplement1, ContentSupplement.class);
        }
        result.setChatMode(ChatMode.fromCode(chatMode));
        result.setContent(content);
        result.setSupplements(supplement);
        return result;
    }

}
