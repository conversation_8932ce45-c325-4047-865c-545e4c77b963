package cn.genn.ai.hub.app.domain.rtc.model.entity;

import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallStatusEnum;
import cn.genn.core.model.enums.DeletedEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Data
public class RTCPitfallInfo {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 智能体id
     */
    private String appId;

    /**
     * 隐患编号
     */
    private String pitfallNo;

    /**
     * 状态,1:UN_SUBMIT-待上报,2:TBC-待确认,3:UN_RESOLVE-待解决,4:RESOLVED-已解决,5:无需解决
     */
    private RTCPitfallStatusEnum status;

    /**
     * 隐患区域
     */
    private String pitfallArea;

    /**
     * 隐患上传内容
     */
    private List<String> uploadContent;

    /**
     * 上报时间
     */
    private LocalDateTime submitTime;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 解决时间
     */
    private LocalDateTime resolveTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createUserId;

    /**
     * 创建者名称
     */
    private String createUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private Long updateUserId;

    /**
     * 修改人名称
     */
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    private DeletedEnum deleted;
}
