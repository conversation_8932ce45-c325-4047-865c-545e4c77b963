package cn.genn.ai.hub.app.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @description 异步线程池和WebMvc异步配置
 * @date 2025-04-10
 */
@Configuration
public class AsyncConfig implements WebMvcConfigurer {

    @Bean
    public Executor virtualThreadExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }

    /**
     * 配置Spring MVC异步任务执行器
     * 解决警告：Performing asynchronous handling through the default Spring MVC SimpleAsyncTaskExecutor
     */
    @Bean("mvcAsyncTaskExecutor")
    public ThreadPoolTaskExecutor mvcAsyncTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("mvc-async-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }

    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 设置异步请求的超时时间（毫秒）
        configurer.setDefaultTimeout(30000);
        // 设置异步任务执行器
        configurer.setTaskExecutor(mvcAsyncTaskExecutor());
    }
}
