package cn.genn.ai.hub.app.application.enums.rtc;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Getter
public enum RTCPitfallConfirmEnum {
    UN_CONFIRM(0, "待确认"),
    NO_NEED_RESOLVE(1, "无需解决"),
    NEED_RESOLVE(2, "需要解决"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    RTCPitfallConfirmEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, RTCPitfallConfirmEnum> VALUES = new HashMap<>();
    static {
        for (final RTCPitfallConfirmEnum item : RTCPitfallConfirmEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RTCPitfallConfirmEnum of(int code) {
        return VALUES.get(code);
    }

}
