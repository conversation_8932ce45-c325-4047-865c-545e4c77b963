package cn.genn.ai.hub.app.application.dto.govow;

import cn.genn.ai.hub.app.application.enums.ChatMode;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.job.xxl.model.task.JobTaskPlan;
import cn.genn.job.xxl.model.task.TaskStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class JobTasksDTO {

    @Schema(description = "任务id")
    private Long id;

    @Schema(description = "任务名称")
    private String name;

    @Schema(description = "任务类型标记")
    private String bizKey;

    @Schema(description = "2.固定频率")
    private JobTaskPlan plan;

    @Schema(description = "cron表达式")
    private String cron;

    @Schema(description = "任务内容")
    private String content;

    @Schema(description = "任务内容")
    private List<ContentSupplement> supplements;

    @Schema(description = "输出模式", example = "common")
    private ChatMode chatMode;

    @Schema(description = "任务是否可以重叠执行,默认false:不可以")
    private Boolean overlap;

    @Schema(description = "执行优先级,0-99数字越小,优先级越高")
    private Integer priority;

    @Schema(description = "任务状态,running自动启用,stop暂停")
    private TaskStatusEnum status;

    @Schema(description = "下次执行时间")
    private LocalDateTime nextTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "头像")
    private String createUserAvatar;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedEnum deleted;
}
