package cn.genn.ai.hub.app.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文本分块任务请求体
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoTaskOfTextChunkBody implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long tenantId;

    private Long repoId;

    private Long collectionId;

    private Long fileId;

    private String contentType;

    /**
     * 用户传入的未分段的原始文本
     */
    private String rawText;

    /**
     * 新增或更新算法那边的分块内容的时候需要
     */
    private String dataKey;

    /**
     * 是否新增或更新算法那边的分块内容
     */
    private Boolean sync = true;

    private Boolean smartChunkSummary;

}

