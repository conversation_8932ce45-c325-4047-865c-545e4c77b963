package cn.genn.ai.hub.app.application.enums.rtc;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Getter
public enum RTCPitfallTypeEnum {
    GENERAL(1, "一般隐患"),
    MAJOR(2, "重大隐患"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    RTCPitfallTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, RTCPitfallTypeEnum> VALUES = new HashMap<>();
    static {
        for (final RTCPitfallTypeEnum item : RTCPitfallTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RTCPitfallTypeEnum of(int code) {
        return VALUES.get(code);
    }

}
