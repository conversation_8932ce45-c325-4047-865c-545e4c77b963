package cn.genn.ai.hub.app.domain.rtc.model.aggregates;

import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallInfo;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallItem;
import cn.genn.ai.hub.app.infrastructure.constant.RTCConstants;
import cn.genn.core.utils.code.biz.BizCodeGenUtils;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-13
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RTCPitfallAgg{

    private RTCPitfallInfo rtcPitfallInfo;

    private List<RTCPitfallItem> rtcPitfallItems;

    public void initPitfallNo() {
        rtcPitfallInfo.setCreateUserName(CurrentUserHolder.getNick());
        rtcPitfallInfo.setUpdateUserName(CurrentUserHolder.getNick());
        rtcPitfallInfo.setPitfallNo(BizCodeGenUtils.generate(RTCConstants.PITFALL_NO_PREFIX,RTCConstants.PITFALL_NO_LENGTH));
        rtcPitfallItems.forEach(item -> item.setPitfallNo(rtcPitfallInfo.getPitfallNo()));
    }
}
