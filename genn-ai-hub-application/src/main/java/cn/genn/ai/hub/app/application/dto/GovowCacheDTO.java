package cn.genn.ai.hub.app.application.dto;

import cn.genn.ai.hub.app.application.enums.DailyAnalysisEnum;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GovowCacheDTO {

    private String cacheKey;

    /**
     * 用户飞书id
     */
    private String openId;
    /**
     * 用户名称
     */
    private String name;

    /**
     * 原始问题
     */
    private String originalQuestion;
    /**
     * 学习与创新
     */
    private String innovations;
    /**
     * 核心工作职责
     */
    private String coreJobs;
    /**
     * 今日工作内容
     */
    private String todayWorks;

    /**
     * 问题简介
     */
    private String questionShort;

    /**
     * 问题详情
     */
    private String questionDetail;

    private String department;

    /**
     * 对话id
     */
    private String chatId;

    /**
     * 首次点击
     */
    private Boolean firstClick = true;

    /**
     * 执行类型
     */
    private DailyAnalysisEnum source = DailyAnalysisEnum.QUESTION;
}
