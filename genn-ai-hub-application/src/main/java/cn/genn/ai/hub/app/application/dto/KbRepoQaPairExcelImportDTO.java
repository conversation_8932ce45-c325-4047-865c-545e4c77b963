package cn.genn.ai.hub.app.application.dto;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 问答对excel导入
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoQaPairExcelImportDTO implements Serializable {

    @ExcelProperty("问题")
    private String question;

    @ExcelProperty("相似问题 (如有多个，请换行分隔)")
    private String similarQuestions;

    @ExcelProperty("答案")
    private String answer;

}

