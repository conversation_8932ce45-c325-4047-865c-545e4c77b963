package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.RTCPitfallAssembler;
import cn.genn.ai.hub.app.application.dto.AgentInfoDTO;
import cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallAnalysisDTO;
import cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallDetailDTO;
import cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallItemDTO;
import cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallPageDTO;
import cn.genn.ai.hub.app.application.query.rtc.RTCPitfallPageQuery;
import cn.genn.ai.hub.app.domain.rtc.model.aggregates.RTCPitfallAgg;
import cn.genn.ai.hub.app.domain.rtc.service.RTCPitfallDomainService;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.RTCPitfallAnalysisMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.RTCPitfallInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.RTCPitfallAnalysisPO;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-13
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RTCPitfallQueryService {

    private final RTCPitfallDomainService rtcPitfallDomainService;
    private final RTCPitfallAssembler rtcPitfallAssembler;
    private final RTCPitfallInfoMapper rtcPitfallInfoMapper;
    private final AgentInfoRepositoryImpl agentInfoRepository;
    private final RTCPitfallAnalysisMapper rtcPitfallAnalysisMapper;

    public RTCPitfallDetailDTO get(IdQuery query) {
        RTCPitfallAgg rtcPitfallAgg = rtcPitfallDomainService.findRTCPitfallAgg(query.getId());
        if (rtcPitfallAgg != null){
            RTCPitfallDetailDTO rtcPitfallDetailDTO = rtcPitfallAssembler.toRTCPitfallDetailDTO(rtcPitfallAgg.getRtcPitfallInfo());
            List<RTCPitfallItemDTO> rtcPitfallItemDTOS = rtcPitfallAssembler.info2RTCPitfallItem(rtcPitfallAgg.getRtcPitfallItems());
            rtcPitfallDetailDTO.setPitfallItems(rtcPitfallItemDTOS);
            AgentInfoDTO agentByWorkflowId = agentInfoRepository.getAgentByWorkflowId(rtcPitfallAgg.getRtcPitfallInfo().getAppId());
            rtcPitfallDetailDTO.setAppName(agentByWorkflowId.getName());
            return rtcPitfallDetailDTO;
        }
        return null;
    }

    public PageResultDTO<RTCPitfallPageDTO> page(RTCPitfallPageQuery query) {
        query.setCreateUserId(CurrentUserHolder.getUserId());
        Page<RTCPitfallPageDTO> rtcPitfallPageDTOPage = rtcPitfallInfoMapper.selectPitfallPage(new Page<>(query.getPageNo(), query.getPageSize()), query);
        if (CollUtil.isNotEmpty(rtcPitfallPageDTOPage.getRecords())){
            rtcPitfallPageDTOPage.getRecords().forEach(rtcPitfallPageDTO -> {
                rtcPitfallPageDTO.setDescription(rtcPitfallPageDTO.getPitfallContent().getDescription());
            });
        }
        return new PageResultDTO((int) rtcPitfallPageDTOPage.getCurrent(), (int) rtcPitfallPageDTOPage.getSize(), rtcPitfallPageDTOPage.getTotal(), rtcPitfallPageDTOPage.getRecords());
    }

    public RTCPitfallAnalysisDTO getSevenAnalysis() {
        List<RTCPitfallAnalysisPO> rtcPitfallAnalysisPOS = rtcPitfallAnalysisMapper.selectList(Wrappers.lambdaQuery(RTCPitfallAnalysisPO.class)
            .eq(RTCPitfallAnalysisPO::getDeleted, DeletedEnum.NOT_DELETED)
            .eq(RTCPitfallAnalysisPO::getCreateUserId, CurrentUserHolder.getUserId())
            .orderByDesc(RTCPitfallAnalysisPO::getId).last("limit 1"));
        if (CollUtil.isEmpty(rtcPitfallAnalysisPOS)){
            return new RTCPitfallAnalysisDTO();
        }
        return rtcPitfallAssembler.toRTCPitfallAnalysisDTO(rtcPitfallAnalysisPOS.getFirst());
    }

    /**
     * 获取最近几天已上报隐患
     * @return
     */
    public List<RTCPitfallPageDTO> getSubmitPitfall(RTCPitfallPageQuery query) {
        return rtcPitfallInfoMapper.selectSubmitPitfallList(query);
    }

}
