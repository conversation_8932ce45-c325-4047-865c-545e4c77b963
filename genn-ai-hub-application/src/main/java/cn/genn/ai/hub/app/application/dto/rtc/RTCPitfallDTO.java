package cn.genn.ai.hub.app.application.dto.rtc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RTCPitfallDTO {

    @Schema(description = "隐患区域")
    private String pitfallArea;

    @Schema(description = "类型,1:GENERAL-一般隐患,2:MAJOR-重大隐患")
    private String pitfallType;

    @Schema(description = "隐患描述")
    private String description;

    @Schema(description = "检查依据")
    private String checkBasis;

    @Schema(description = "整改措施")
    private String measure;

    @Schema(description = "解决方案")
    private String solution;

}
