package cn.genn.ai.hub.app.interfaces.govow;

import cn.genn.ai.hub.app.application.command.GovowFsQuestionAddCacheCommand;
import cn.genn.ai.hub.app.application.dto.GovowCacheDTO;
import cn.genn.ai.hub.app.application.enums.DailyAnalysisEnum;
import cn.genn.ai.hub.app.application.listener.govow.GovowFsQuestionPushParam;
import cn.genn.ai.hub.app.application.service.govow.GovowOperationService;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "格物运营")
@RestController
@RequiredArgsConstructor
@RequestMapping("/govow/operation")
public class GovowOperationController {

    private final GovowOperationService govowOperationService;

    @PostMapping("/addAgentCache")
    @Operation(summary = "运营agent添加缓存信息")
    @IgnoreTenant
    public void addAgentCache(@RequestBody @Validated GovowFsQuestionAddCacheCommand command) {
        govowOperationService.addAgentCache(command);
    }


    @GetMapping("/buryingPoint")
    @Operation(summary = "卡片跳转格物埋点")
    @IgnoreTenant
    public GovowCacheDTO buryingPoint(@RequestParam String cacheKey) {
        return govowOperationService.buryingPoint(cacheKey);
    }

    @GetMapping("/test")
    @Operation(summary = "测试发送")
    @IgnoreTenant
    public void test(@RequestBody  GovowFsQuestionPushParam param) {
        govowOperationService.pushQuestionToFeishu(param, DailyAnalysisEnum.QUESTION);
    }
}
