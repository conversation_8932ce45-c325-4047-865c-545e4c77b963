package cn.genn.ai.hub.app.domain.rtc.model.entity;

import cn.genn.ai.hub.app.application.dto.rtc.RTCPitfallContentDTO;
import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallConfirmEnum;
import cn.genn.ai.hub.app.application.enums.rtc.RTCPitfallTypeEnum;
import cn.genn.core.model.enums.DeletedEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
@Data
public class RTCPitfallItem {

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 隐患信息id
     */
    private Long pitfallId;

    /**
     * 隐患编号
     */
    private String pitfallNo;

    /**
     * 类型,1:GENERAL-一般隐患,2:MAJOR-重大隐患
     */
    private RTCPitfallTypeEnum pitfallType;

    /**
     * 确认状态,0:UN_CONFIRM-待确认,1:NO_NEED_RESOLVE-无需解决,2:NEED_RESOLVE-需要解决
     */
    private RTCPitfallConfirmEnum confirmStatus;

    /**
     * 隐患内容
     */
    private RTCPitfallContentDTO pitfallContent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createUserId;

    /**
     * 创建者名称
     */
    private String createUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private Long updateUserId;

    /**
     * 修改人名称
     */
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    private DeletedEnum deleted;
}
