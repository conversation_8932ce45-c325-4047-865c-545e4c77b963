package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum TaskTypeEnum {

    FILE_PARSE("file_parse", "文件解析"),
    TEXT_CHUNK("text_chunk", "文本分块"),
    INDEX_VECTOR("index_vector", "索引向量化"),
    QA_PAIR_VECTOR("qa_pair_vector", "问答对向量化"),
    SMART_CHUNK_SUMMARY("smart_chunk_summary", "智能分块摘要"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    TaskTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}

