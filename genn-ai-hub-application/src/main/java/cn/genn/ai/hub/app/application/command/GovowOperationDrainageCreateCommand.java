package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.DailyAnalysisEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import lombok.experimental.Accessors;

/**
 * 格物运营-用户引流创建命令
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "格物运营-用户引流创建命令")
@Accessors(chain = true)
public class GovowOperationDrainageCreateCommand {

    @Schema(description = "缓存键")
    @NotBlank(message = "缓存键不能为空")
    private String cacheKey;

    @Schema(description = "问题简介")
    @NotBlank(message = "问题简介不能为空")
    private String questionShort;

    @Schema(description = "问题详情")
    @NotBlank(message = "问题详情不能为空")
    private String questionDetail;

    @Schema(description = "部门")
    private String department;

    @Schema(description = "其他信息")
    private String extraInfo;

    @Schema(description = "飞书用户ID")
    private String openId;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "对话id")
    private String chatId;

    @Schema(description = "执行类型")
    private DailyAnalysisEnum source;

}
