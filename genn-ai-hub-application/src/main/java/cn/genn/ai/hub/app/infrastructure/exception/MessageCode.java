package cn.genn.ai.hub.app.infrastructure.exception;

import cn.genn.core.exception.MessageCodeWrap;

/**
 * 业务错误码定义
 * 从201-899
 *
 * <AUTHOR>
 */
public enum MessageCode implements MessageCodeWrap {

    RESOURCE_NOT_EXIST("201", "资源不存在"),
    QUEUE_MAX("202", "队列满了"),
    KB_REPO_FILE_NOT_EXIST("203", "知识库文件不存在"),
    KB_REPO_COLLECTION_CREATE_FAIL("204", "知识库数据集创建失败"),
    COLLECTION_HAS_CHILDREN("205", "存在下级目录或文件,不允许删除"),
    KB_REPO_FILE_CHECK_EXCEL_FAIL("206", "文件格式不正确，只支持.xlsx或.xls格式"),
    IMPORT_EXCEL_HEAD_NOT_MATCH("207", "导入文件表头字段同模版不匹配，请重新确认上传的文件"),
    KB_REPO_TERM_NAME_EXIST("208", "该术语已经存在"),
    KB_REPO_TERM_IMPORT_EXCEL_NO_DATA("209", "术语名称重复,导入失败"),
    TELEPHONE_NOT_EXIST("210", "手机号未注册!"),

    AUTH_API_AUTH_FAILED("401", "不允许访问该资源"),

    HTTP_ERROR("500", "HTTP服务请求异常"),

    MODEL_EXIST("301", "模型已存在"),

    KB_CATE_NOT_DELETE("401", "存在下级目录，不允许删除"),
    TOS_STS_CONFIG_ERROR("402", "获取云存储平台的配置失败"),
    TAG_NOT_FOUND("403", "标签不存在"),

    ONLY_CREATOR_CAN_INVITE("600", "只有团队创建者才能邀请其他用户"),
    ONLY_CREATOR_CAN_KICKOUT("601", "只有团队创建者才能踢出成员"),
    CANNOT_KICKOUT_SELF("602", "不能踢出自己"),
    USER_NOT_TEAM_MEMBER("603", "该用户不是团队成员"),
    CREATOR_CANNOT_LEAVE("604", "创建者不能退出团队"),
    ONLY_CREATOR_CAN_TRANSFER("605", "只有团队创建者才能转让团队"),
    CANNOT_TRANSFER_TO_SELF("606", "不能转让给自己"),
    TRANSFER_TARGET_NOT_MEMBER("607", "转让目标不是团队成员"),
    USER_NOT_EXIST("608", "用户不存在"),
    USER_NOT_VALID("609", "用户不合法"),
    PERSONAL_SPACE_NOT_INVITE("610", "个人空间不允许邀请协作"),
    NOT_AGENT_CREATOR("611", "不是智能体创建者,无法邀请"),
    TEAM_SPACE_NOT_TRANSFER("612", "团队空间不允许转移"),
    TAG_TYPE_GENERAL_TOOL_NOT_SUPPORT("613", "通用工具不支持该操作"),
    TEAM_HAS_MEMBERS("614", "团队存在成员，不允许删除"),
    TEAM_HAS_RESOURCE("615", "团队存在资源，不允许删除"),
    AGENT_LOCKED("616", "智能体正在被其他成员编辑,请稍后操作!"),
    PERSONAL_SPACE_NOT_REMOVE("617", "个人空间不允许移除协作"),
    TEAM_OWNER_NOT_REMOVE("618", "不允许移除团队创建者"),
    TEAM_RESOURCE_NOT_TRANSFER("619", "团队资源不允许转移"),
    INVALID_RESOURCE("620", "无效的资源"),
    MCP_SERVER_RESTART_FAILED("621", "重启MCP服务失败"),
    MCP_CONFIG_NOT_VALID("622", "MCP配置不合法, {0}"),
    MCP_GROUP_PUBLISHED("623", "MCP分组已发布,不允许删除"),
    INVALID_STORAGE_CONFIG("700", "无效的存储配置"),;

    private final String code;
    private final String description;

    MessageCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
