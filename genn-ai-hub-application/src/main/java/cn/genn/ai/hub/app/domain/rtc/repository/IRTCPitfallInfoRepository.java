package cn.genn.ai.hub.app.domain.rtc.repository;

import cn.genn.ai.hub.app.domain.rtc.model.aggregates.RTCPitfallAgg;
import cn.genn.ai.hub.app.domain.rtc.model.entity.RTCPitfallAnalysisInfo;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-12
 */
public interface IRTCPitfallInfoRepository {

    RTCPitfallAgg selectRTCPitfallAgg(Long id);

    void add(RTCPitfallAgg rtcPitfallAgg);

    void update(RTCPitfallAgg rtcPitfallAgg);

    void addPitfallAnalysis(RTCPitfallAnalysisInfo rtcPitfallAnalysisInfo);

    void save(RTCPitfallAgg rtcPitfallAgg);
}
