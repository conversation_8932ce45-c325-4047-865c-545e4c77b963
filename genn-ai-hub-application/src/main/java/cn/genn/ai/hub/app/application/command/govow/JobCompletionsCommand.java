package cn.genn.ai.hub.app.application.command.govow;

import cn.genn.ai.hub.app.application.dto.govow.ContentSupplement;
import cn.genn.ai.hub.app.application.enums.ChatMode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.NotBlank;

import java.util.List;

@Data
@Accessors(chain = true)
public class JobCompletionsCommand {

    @Schema(description = "用户id", example = "common")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "输出模式", example = "common")
    @NotNull(message = "输出模式不能为空")
    private ChatMode chatMode;

    @Schema(description = "任务内容")
    @NotBlank(message = "任务内容不能为空")
    private String content;

    @Schema(description = "任务内容")
    private List<ContentSupplement> supplements;
}
