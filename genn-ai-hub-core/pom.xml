<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.genn.app</groupId>
        <artifactId>genn-ai-hub</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>genn-ai-hub-core</artifactId>
    <version>1.3.0-RELEASE</version>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.genn.nova</groupId>
            <artifactId>genn-core</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-ai</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>
