<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.genn.app</groupId>
        <artifactId>genn-ai-hub</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>genn-ai-hub-plugin</artifactId>
    <version>1.3.3-RELEASE</version>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <feishu-sdk.version>2.4.12</feishu-sdk.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-ai-hub-core</artifactId>
            <version>1.3.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-web</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-database</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-ai</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.larksuite.oapi</groupId>
            <artifactId>oapi-sdk</artifactId>
            <version>${feishu-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-upm</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.volcengine</groupId>
            <artifactId>volc-sdk-java</artifactId>
            <version>1.0.230</version>
        </dependency>
    </dependencies>

</project>
